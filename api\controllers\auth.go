package controllers

import (
	"crypto/md5"
	"encoding/hex"
	"log"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm/clause"

	. "lottery/database"
	. "lottery/models"
	. "lottery/utils"
)

// 創建一個WebSocket連接映射表
var wsConnections = make(map[string]map[string]*websocket.Conn) // 用戶ID -> 設備ID -> 連接
// 設置 WebSocket upgrader
var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	// 允許所有跨域請求
	CheckOrigin: func(r *http.Request) bool {
		return true // 在生產環境中，您應該僅允許特定的來源
	},
	HandshakeTimeout: 5 * time.Second,
}

func RegisterHandler(c *gin.Context) {
	var req RegisterRequest

	if err := c.ShouldBind(&req); err != nil {
		c.JSO<PERSON>(http.StatusBadRequest, ErrorMsg{
			Error: err.Error(),
			Msg:   "資料不完整",
		})
		return
	}

	// 檢查是否有重複的 UID
	if IsUserExists(req.UID) {
		c.JSON(http.StatusConflict, ErrorMsg{
			Error: "此帳號已被註冊",
			Msg:   "此帳號已被註冊",
		})
		return
	}

	// 密碼加密
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Pwd), bcrypt.DefaultCost)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorMsg{
			Error: err.Error(),
			Msg:   "註冊失敗",
		})
		return
	}

	user := User{
		UID:     req.UID,
		Pwd:     string(hashedPassword),
		IsAdmin: false,
		Name:    req.Name,
		Email:   req.Email,
	}

	db := ConnectDB()
	defer CloseDB(db)

	if err := db.Create(&user).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorMsg{
			Error: err.Error(),
			Msg:   "註冊失敗",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "註冊成功"})
}

func LoginHandler(c *gin.Context) {
	var req LoginRequest

	if err := c.ShouldBind(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorMsg{
			Error: err.Error(),
			Msg:   "資料不完整",
		})
		return
	}

	db := ConnectDB()
	defer CloseDB(db)

	user, err := GetUserByUID(req.UID)
	if err != nil {
		c.JSON(http.StatusUnauthorized, ErrorMsg{
			Error: err.Error(),
			Msg:   "帳號或密碼錯誤",
		})
		return
	}

	loginLog := LoginLog{
		UserID:    user.ID,
		IPAddress: c.ClientIP(),
		UserAgent: c.Request.UserAgent(),
		IsSuccess: false,
	}

	// 檢查密碼
	if err := bcrypt.CompareHashAndPassword([]byte(user.Pwd), []byte(req.Pwd)); err != nil {
		db.Create(&loginLog)
		c.JSON(http.StatusUnauthorized, ErrorMsg{
			Error: err.Error(),
			Msg:   "帳號或密碼錯誤",
		})
		return
	}

	if !user.IsValid() {
		db.Create(&loginLog)
		c.JSON(http.StatusForbidden, ErrorMsg{
			Error: "帳號尚未啟用，請聯絡管理員",
			Msg:   "帳號尚未啟用，請聯絡管理員",
		})
		return
	}

	// 生成設備ID（優化版本，支援前端設備指紋）
	userAgent := c.Request.UserAgent()
	clientIP := c.ClientIP()

	var deviceID, stableDeviceID string
	var confidence int

	if req.DeviceFingerprint != nil && req.DeviceFingerprint.StableDeviceID != "" {
		// 使用前端提供的設備指紋
		stableDeviceID = req.DeviceFingerprint.StableDeviceID
		deviceID = generateEnhancedDeviceID(userAgent, clientIP, req.DeviceFingerprint)
		confidence = req.DeviceFingerprint.Confidence
		log.Printf("🔍 使用前端設備指紋: StableID=%s, Confidence=%d", stableDeviceID[:8]+"...", confidence)
	} else {
		// 降級到傳統方法
		deviceID = generateDeviceID(userAgent, clientIP, time.Now().UnixNano())
		stableDeviceID = generateStableDeviceID(userAgent)
		confidence = 50
		log.Printf("⚠️ 使用傳統設備識別: DeviceID=%s", deviceID[:8]+"...")
	}

	deviceName := inferDeviceName(userAgent)

	// 強制單一設備登入：異步通知並清理所有現有設備的WebSocket連接
	go notifyAndCleanupUserDevices(user.ID, deviceID, "您的帳號已在另一個設備登入")

	// 將所有該用戶的設備設為非活躍
	db.Model(&UserDevice{}).Where("user_id = ?", user.ID).Update("is_active", false)

	// 異步吊銷該用戶所有有效的訪問令牌，避免阻塞登入流程
	go func() {
		// 先獲取要撤銷的access token IDs
		var accessTokenIDs []uint64
		db.Model(&AccessToken{}).
			Where("user_id = ? AND is_revoked = ? AND expires_at > ?", user.ID, false, time.Now()).
			Pluck("id", &accessTokenIDs)

		if len(accessTokenIDs) > 0 {
			// 撤銷access tokens
			db.Model(&AccessToken{}).
				Where("id IN ?", accessTokenIDs).
				Update("is_revoked", true)

			// 撤銷對應的refresh tokens
			db.Model(&RefreshToken{}).
				Where("access_token_id IN ?", accessTokenIDs).
				Update("is_revoked", true)
		}
	}()

	// 使用ON DUPLICATE KEY UPDATE避免競爭條件
	deviceRecord := UserDevice{
		UserID:         user.ID,
		DeviceID:       deviceID,
		StableDeviceID: stableDeviceID,
		DeviceName:     deviceName,
		UserAgent:      userAgent,
		IsActive:       true,
		LastSeenAt:     time.Now(),
		Confidence:     confidence,
	}

	// 使用GORM的Clauses進行upsert操作
	result := db.Clauses(clause.OnConflict{
		Columns: []clause.Column{{Name: "user_id"}, {Name: "device_id"}},
		DoUpdates: clause.AssignmentColumns([]string{
			"stable_device_id", "device_name", "user_agent", "is_active", "last_seen_at", "confidence", "updated_at",
		}),
	}).Create(&deviceRecord)

	if result.Error != nil {
		errorMsg := ErrorMsg{
			Error: result.Error.Error(),
			Msg:   "設備記錄更新失敗",
		}
		ErrorLog(errorMsg)
	}

	// 產生 Token
	s := NewTokenService(db)
	tokenPair, err := s.GenerateTokenPair(user, userAgent, c.ClientIP(), deviceID)
	if err != nil {
		errorMsg := ErrorMsg{
			Error: err.Error(),
			Msg:   "登入失敗",
		}
		ErrorLog(errorMsg)
		c.JSON(http.StatusInternalServerError, errorMsg)
		return
	}

	// 更新最後登入時間
	db.Model(&user).Update("last_login_at", time.Now())

	loginLog.IsSuccess = true
	db.Create(&loginLog)

	c.JSON(http.StatusOK, LoginResponse{
		TokenPair: tokenPair,
		User: LoginResponseUser{
			ID:   user.ID,
			UID:  user.UID,
			Name: user.Name,
		},
	})
}

// 生成設備ID
func generateDeviceID(userAgent string, ip string, timestamp ...int64) string {
	// 使用用戶代理、IP地址和可選的時間戳生成設備ID
	hasher := md5.New()
	data := userAgent + ip

	// 如果提供了時間戳，加入以確保會話唯一性
	if len(timestamp) > 0 {
		data += strconv.FormatInt(timestamp[0], 10)
	}

	hasher.Write([]byte(data))
	return hex.EncodeToString(hasher.Sum(nil))
}

// 生成增強設備ID（使用前端指紋）
func generateEnhancedDeviceID(userAgent string, ip string, fingerprint *DeviceFingerprint) string {
	hasher := md5.New()

	// 使用穩定的設備特徵 + 會話ID
	data := fingerprint.StableDeviceID + fingerprint.SessionID + fingerprint.Platform + fingerprint.Screen + userAgent

	hasher.Write([]byte(data))
	return hex.EncodeToString(hasher.Sum(nil))
}

// 生成穩定設備ID（不包含會話信息）
func generateStableDeviceID(userAgent string) string {
	hasher := md5.New()

	// 提取穩定的設備特徵
	stableFeatures := extractStableFeatures(userAgent)
	hasher.Write([]byte(stableFeatures))
	return hex.EncodeToString(hasher.Sum(nil))
}

// 提取穩定的設備特徵
func extractStableFeatures(userAgent string) string {
	ua := strings.ToLower(userAgent)
	features := []string{}

	// 提取操作系統信息
	if strings.Contains(ua, "windows") {
		if match := regexp.MustCompile(`windows nt ([\d\.]+)`).FindStringSubmatch(ua); len(match) > 1 {
			features = append(features, "windows-"+match[1])
		} else {
			features = append(features, "windows")
		}
	} else if strings.Contains(ua, "mac os x") {
		if match := regexp.MustCompile(`mac os x ([\d_\.]+)`).FindStringSubmatch(ua); len(match) > 1 {
			features = append(features, "macos-"+strings.Replace(match[1], "_", ".", -1))
		} else {
			features = append(features, "macos")
		}
	} else if strings.Contains(ua, "android") {
		if match := regexp.MustCompile(`android ([\d\.]+)`).FindStringSubmatch(ua); len(match) > 1 {
			features = append(features, "android-"+match[1])
		} else {
			features = append(features, "android")
		}
	} else if strings.Contains(ua, "iphone") || strings.Contains(ua, "ipad") {
		if match := regexp.MustCompile(`os ([\d_]+)`).FindStringSubmatch(ua); len(match) > 1 {
			features = append(features, "ios-"+strings.Replace(match[1], "_", ".", -1))
		} else {
			features = append(features, "ios")
		}
	}

	// 提取設備型號（移動設備）
	if strings.Contains(ua, "mobile") || strings.Contains(ua, "android") {
		// Samsung
		if match := regexp.MustCompile(`sm-[a-z0-9]+`).FindString(ua); match != "" {
			features = append(features, "samsung-"+match)
		}
		// iPhone
		if match := regexp.MustCompile(`iphone[0-9]+,[0-9]+`).FindString(ua); match != "" {
			features = append(features, "iphone-"+match)
		}
	}

	return strings.Join(features, "|")
}

// inferDeviceName 從用戶代理字符串推斷設備名稱
func inferDeviceName(userAgent string) string {
	userAgent = strings.ToLower(userAgent)

	// 操作系統映射
	osNames := map[string]string{
		"windows nt 10":  "Windows 10",
		"windows nt 6.3": "Windows 8.1",
		"windows nt 6.2": "Windows 8",
		"windows nt 6.1": "Windows 7",
		"windows nt 6.0": "Windows Vista",
		"windows nt 5.1": "Windows XP",
		"macintosh":      "Mac",
		"mac os x":       "Mac OS X",
		"iphone":         "iPhone",
		"ipad":           "iPad",
		"android":        "Android",
		"linux":          "Linux",
		"ubuntu":         "Ubuntu",
		"fedora":         "Fedora",
		"cros":           "Chrome OS",
	}

	// 瀏覽器映射
	browserNames := map[string]string{
		"chrome":         "Chrome",
		"firefox":        "Firefox",
		"safari":         "Safari",
		"opera":          "Opera",
		"msie":           "Internet Explorer",
		"trident":        "Internet Explorer",
		"edge":           "Edge",
		"edg":            "Edge",
		"seamonkey":      "SeaMonkey",
		"ucbrowser":      "UC Browser",
		"qqbrowser":      "QQ Browser",
		"micromessenger": "WeChat",
		"wechat":         "WeChat",
	}

	// 移動設備型號
	mobileModels := map[string]string{
		"sm-":     "Samsung",
		"samsung": "Samsung",
		"huawei":  "Huawei",
		"honor":   "Honor",
		"mi":      "Xiaomi",
		"redmi":   "Redmi",
		"oneplus": "OnePlus",
		"pixel":   "Google Pixel",
		"lg-":     "LG",
		"moto":    "Motorola",
		"nokia":   "Nokia",
		"sony":    "Sony",
		"htc":     "HTC",
		"lenovo":  "Lenovo",
		"asus":    "Asus",
		"oppo":    "OPPO",
		"vivo":    "Vivo",
	}

	// 推斷操作系統
	var osName string
	for key, name := range osNames {
		if strings.Contains(userAgent, key) {
			osName = name
			break
		}
	}

	// 推斷瀏覽器
	var browserName string
	for key, name := range browserNames {
		if strings.Contains(userAgent, key) {
			browserName = name
			break
		}
	}

	// 推斷移動設備型號（僅當是移動設備時）
	var modelName string
	if strings.Contains(userAgent, "mobile") ||
		strings.Contains(userAgent, "android") ||
		strings.Contains(userAgent, "iphone") {
		for key, name := range mobileModels {
			if strings.Contains(userAgent, key) {
				modelName = name
				break
			}
		}
	}

	// 從 User-Agent 提取更具體的設備信息
	deviceInfo := extractDeviceInfo(userAgent)

	// 組合設備名稱
	var deviceParts []string

	// 添加移動設備型號（如果有）
	if modelName != "" {
		if deviceInfo != "" {
			deviceParts = append(deviceParts, modelName+" "+deviceInfo)
		} else {
			deviceParts = append(deviceParts, modelName)
		}
	}

	// 添加操作系統（如果有）
	if osName != "" {
		deviceParts = append(deviceParts, osName)
	}

	// 添加瀏覽器（如果有）
	if browserName != "" {
		deviceParts = append(deviceParts, browserName)
	}

	// 如果無法推斷任何信息，返回通用名稱
	if len(deviceParts) == 0 {
		return "Unknown Device"
	}

	return strings.Join(deviceParts, " - ")
}

// extractDeviceInfo 嘗試從 User-Agent 提取更具體的設備信息（如型號）
func extractDeviceInfo(userAgent string) string {
	// 常見的移動設備型號格式匹配

	// Samsung 格式: SM-G950F, SM-N975F 等
	smMatch := regexp.MustCompile(`SM-[A-Z0-9]+`).FindString(userAgent)
	if smMatch != "" {
		return smMatch
	}

	// iPhone 格式: iPhone13,4, iPhone10,6 等或 iPhone OS 14_2 等
	iphoneModelMatch := regexp.MustCompile(`iPhone[0-9]+,[0-9]+`).FindString(userAgent)
	if iphoneModelMatch != "" {
		return iphoneModelMatch
	}

	// iPad 格式
	ipadModelMatch := regexp.MustCompile(`iPad[0-9]+,[0-9]+`).FindString(userAgent)
	if ipadModelMatch != "" {
		return ipadModelMatch
	}

	// 提取 iOS 版本
	iosVersionMatch := regexp.MustCompile(`OS ([0-9_]+) like Mac OS X`).FindStringSubmatch(userAgent)
	if len(iosVersionMatch) > 1 {
		iosVersion := strings.Replace(iosVersionMatch[1], "_", ".", -1)
		return "iOS " + iosVersion
	}

	// 提取 Android 版本
	androidVersionMatch := regexp.MustCompile(`Android ([0-9\.]+)`).FindStringSubmatch(userAgent)
	if len(androidVersionMatch) > 1 {
		return "Android " + androidVersionMatch[1]
	}

	// Huawei 格式: HUAWEI MHA-L29, HUAWEI P30 等
	huaweiMatch := regexp.MustCompile(`HUAWEI [A-Z0-9\-]+`).FindString(userAgent)
	if huaweiMatch != "" {
		return huaweiMatch
	}

	// 小米格式: MI 9, Redmi Note 8 Pro 等
	miMatch := regexp.MustCompile(`(MI|Redmi) [A-Za-z0-9 ]+`).FindString(userAgent)
	if miMatch != "" {
		return miMatch
	}

	// OnePlus 格式: OnePlus7T 等
	oneplusMatch := regexp.MustCompile(`OnePlus[A-Za-z0-9]+`).FindString(userAgent)
	if oneplusMatch != "" {
		return oneplusMatch
	}

	// OPPO 格式
	oppoMatch := regexp.MustCompile(`OPPO [A-Za-z0-9]+`).FindString(userAgent)
	if oppoMatch != "" {
		return oppoMatch
	}

	// 其他一般格式的設備識別
	deviceMatch := regexp.MustCompile(`\(([^;]+);`).FindStringSubmatch(userAgent)
	if len(deviceMatch) > 1 && !strings.Contains(strings.ToLower(deviceMatch[1]), "windows") &&
		!strings.Contains(strings.ToLower(deviceMatch[1]), "macintosh") &&
		!strings.Contains(strings.ToLower(deviceMatch[1]), "linux") {
		return deviceMatch[1]
	}

	return ""
}

// WebSocket處理函數
func HandleWebSocket(c *gin.Context) {
	userID := c.GetString("user_id")
	deviceID := c.GetString("device_id")

	// 升級HTTP連接為WebSocket
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		// WebSocket升級失敗時不能使用c.JSON，直接返回
		return
	}

	// 清理該用戶的舊連接（如果存在相同設備ID的連接）
	if userDevices, exists := wsConnections[userID]; exists {
		if oldConn, deviceExists := userDevices[deviceID]; deviceExists {
			oldConn.Close()
		}
	}

	// 將連接添加到映射表
	if _, exists := wsConnections[userID]; !exists {
		wsConnections[userID] = make(map[string]*websocket.Conn)
	}
	wsConnections[userID][deviceID] = conn

	// 檢查是否有待處理的強制登出通知
	go checkPendingLogoutNotification(userID, deviceID, conn)

	// 在連接關閉時清理
	defer func() {
		conn.Close()

		// 安全地清理映射表
		if userDevices, exists := wsConnections[userID]; exists {
			delete(userDevices, deviceID)

			// 如果用戶沒有其他連接，清理用戶映射
			if len(userDevices) == 0 {
				delete(wsConnections, userID)
			}
		}
	}()

	// 處理接收到的消息
	for {
		_, _, err := conn.ReadMessage()
		if err != nil {
			break
		}
	}
}

// 通知用戶的所有設備登出
func notifyAllUserDevices(userID uint64, message string) {
	userIDStr := strconv.FormatUint(userID, 10)

	// 檢查該用戶是否有活躍的WebSocket連接
	devices, exists := wsConnections[userIDStr]
	if !exists {
		return
	}

	// 向該用戶的所有設備發送登出通知
	for deviceID, conn := range devices {
		// 發送登出消息
		logoutMsg := gin.H{
			"type":    "forced_logout",
			"message": message,
		}

		if err := conn.WriteJSON(logoutMsg); err != nil {
			// 如果發送失敗，關閉連接
			conn.Close()
			delete(devices, deviceID)
		}
	}
}

// 通知其他設備登出（保留原函數以備其他地方使用）
func notifyOtherDevices(userID uint64, excludeDeviceID string) {
	userIDStr := strconv.FormatUint(userID, 10)

	// 檢查該用戶是否有活躍的WebSocket連接
	devices, exists := wsConnections[userIDStr]
	if !exists {
		return
	}

	// 向該用戶的其他設備發送登出通知
	for deviceID, conn := range devices {
		if deviceID != excludeDeviceID {
			// 發送登出消息
			logoutMsg := gin.H{
				"type":    "forced_logout",
				"message": "您的帳號已在另一個設備登入",
			}

			if err := conn.WriteJSON(logoutMsg); err != nil {
				// 如果發送失敗，關閉連接
				conn.Close()
				delete(devices, deviceID)
			}
		}
	}
}

func LogoutHandler(c *gin.Context) {
	token := GetTokenFromHeader(c)
	if token == "" {
		c.JSON(http.StatusBadRequest, ErrorMsg{
			Error: "未提供 Token",
		})
		return
	}

	db := ConnectDB()
	defer CloseDB(db)

	s := NewTokenService(db)

	claims, _, err := s.ParseToken(token)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorMsg{
			Error: err.Error(),
			Msg:   "未提供 Token",
		})
		return
	}

	if err := s.RevokeToken(claims.ID); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorMsg{
			Error: err.Error(),
			Msg:   "登出失敗",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "成功登出"})
}

func RefreshTokenHandler(c *gin.Context) {
	// 從請求中獲取 Refresh Token
	refreshToken := c.GetHeader("X-Refresh-Token")
	if refreshToken == "" {
		errMsg := ErrorMsg{
			Error: "Refresh token is missing",
		}
		ErrorLog(errMsg)
		c.JSON(http.StatusUnauthorized, errMsg)
		return
	}

	db := ConnectDB()
	defer CloseDB(db)

	s := NewTokenService(db)

	tokenPair, err := s.RefreshToken(refreshToken)
	if err != nil {
		errMsg := ErrorMsg{
			Error: err.Error(),
			Msg:   "Refresh token is invalid or expired",
		}
		ErrorLog(errMsg)
		c.JSON(http.StatusUnauthorized, errMsg)
		return
	}

	c.JSON(http.StatusOK, TokenPair{
		AccessToken:      tokenPair.AccessToken,
		ExpiresAt:        tokenPair.ExpiresAt,
		RefreshToken:     tokenPair.RefreshToken,
		RefreshExpiresAt: tokenPair.RefreshExpiresAt,
	})
}

// 獲取用戶設備列表 (管理員專用)
func GetUserDevices(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, ErrorMsg{
			Error: "用戶ID不能為空",
			Msg:   "用戶ID不能為空",
		})
		return
	}

	db := ConnectDB()
	defer CloseDB(db)

	var devices []UserDevice
	if err := db.Where("user_id = ?", userID).Order("created_at DESC").Find(&devices).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorMsg{
			Error: err.Error(),
			Msg:   "獲取設備列表失敗",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"devices": devices,
	})
}

// 獲取用戶登入歷史 (管理員專用)
func GetUserLoginHistory(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, ErrorMsg{
			Error: "用戶ID不能為空",
			Msg:   "用戶ID不能為空",
		})
		return
	}

	db := ConnectDB()
	defer CloseDB(db)

	var loginLogs []LoginLog
	if err := db.Where("user_id = ?", userID).Order("created_at DESC").Limit(50).Find(&loginLogs).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorMsg{
			Error: err.Error(),
			Msg:   "獲取登入歷史失敗",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"login_history": loginLogs,
	})
}

// 刪除用戶設備 (管理員專用)
func DeleteUserDevice(c *gin.Context) {
	userID := c.Param("id")
	deviceID := c.Param("device_id")

	if userID == "" || deviceID == "" {
		c.JSON(http.StatusBadRequest, ErrorMsg{
			Error: "用戶ID和設備ID不能為空",
			Msg:   "用戶ID和設備ID不能為空",
		})
		return
	}

	db := ConnectDB()
	defer CloseDB(db)

	// 軟刪除設備
	if err := db.Where("user_id = ? AND device_id = ?", userID, deviceID).Delete(&UserDevice{}).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorMsg{
			Error: err.Error(),
			Msg:   "刪除設備失敗",
		})
		return
	}

	// 吊銷該設備的所有有效token
	db.Model(&AccessToken{}).Where("user_id = ? AND device_id = ? AND is_revoked = ? AND expires_at > ?", userID, deviceID, false, time.Now()).
		Update("is_revoked", true)

	// 通知該設備登出
	notifyDeviceLogout(userID, deviceID)

	c.JSON(http.StatusOK, gin.H{
		"message": "設備已刪除",
	})
}

// 通知特定設備登出
func notifyDeviceLogout(userID, deviceID string) {
	// 檢查該用戶是否有活躍的WebSocket連接
	devices, exists := wsConnections[userID]
	if !exists {
		return
	}

	// 向指定設備發送登出通知
	if conn, exists := devices[deviceID]; exists {
		logoutMsg := gin.H{
			"type":    "forced_logout",
			"message": "您的設備已被管理員移除",
		}

		if err := conn.WriteJSON(logoutMsg); err != nil {
			// 如果發送失敗，關閉連接
			conn.Close()
			delete(devices, deviceID)
		}
	}
}

// GetCurrentUserProfile 獲取當前用戶的個人資料
func GetCurrentUserProfile(c *gin.Context) {
	// 從 token 中獲取用戶 ID
	token := GetTokenFromHeader(c)
	if token == "" {
		c.JSON(http.StatusBadRequest, ErrorMsg{
			Error: "未提供 Token",
			Msg:   "未提供 Token",
		})
		return
	}

	db := ConnectDB()
	defer CloseDB(db)

	s := NewTokenService(db)
	claims, _, err := s.ParseToken(token)
	if err != nil {
		c.JSON(http.StatusUnauthorized, ErrorMsg{
			Error: err.Error(),
			Msg:   "Token 無效",
		})
		return
	}

	user, err := GetUserByID(claims.UserID)
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorMsg{
			Error: err.Error(),
			Msg:   "用戶不存在",
		})
		return
	}

	// 返回用戶資料（不包含密碼）
	c.JSON(http.StatusOK, gin.H{
		"id":            user.ID,
		"uid":           user.UID,
		"name":          user.Name,
		"email":         user.Email,
		"is_active":     user.IsActive,
		"expires_at":    user.ExpiresAt,
		"last_login_at": user.LastLoginAt,
		"created_at":    user.CreatedAt,
	})
}

// UpdateCurrentUserProfile 更新當前用戶的個人資料
func UpdateCurrentUserProfile(c *gin.Context) {
	// 從 token 中獲取用戶 ID
	token := GetTokenFromHeader(c)
	if token == "" {
		c.JSON(http.StatusBadRequest, ErrorMsg{
			Error: "未提供 Token",
			Msg:   "未提供 Token",
		})
		return
	}

	db := ConnectDB()
	defer CloseDB(db)

	s := NewTokenService(db)
	claims, _, err := s.ParseToken(token)
	if err != nil {
		c.JSON(http.StatusUnauthorized, ErrorMsg{
			Error: err.Error(),
			Msg:   "Token 無效",
		})
		return
	}

	var req struct {
		Name  string `json:"name" binding:"required"`
		Email string `json:"email"`
	}

	if err := c.ShouldBind(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorMsg{
			Error: err.Error(),
			Msg:   "資料不完整",
		})
		return
	}

	// 更新用戶資料
	if err := db.Model(&User{}).Where("id = ?", claims.UserID).Updates(map[string]interface{}{
		"name":  req.Name,
		"email": req.Email,
	}).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorMsg{
			Error: err.Error(),
			Msg:   "更新失敗",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "個人資料更新成功",
	})
}

// ChangePassword 更改密碼
func ChangePassword(c *gin.Context) {
	// 從 token 中獲取用戶 ID
	token := GetTokenFromHeader(c)
	if token == "" {
		c.JSON(http.StatusBadRequest, ErrorMsg{
			Error: "未提供 Token",
			Msg:   "未提供 Token",
		})
		return
	}

	db := ConnectDB()
	defer CloseDB(db)

	s := NewTokenService(db)
	claims, _, err := s.ParseToken(token)
	if err != nil {
		c.JSON(http.StatusUnauthorized, ErrorMsg{
			Error: err.Error(),
			Msg:   "Token 無效",
		})
		return
	}

	var req struct {
		NewPassword string `json:"new_password" binding:"required"`
	}

	if err := c.ShouldBind(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorMsg{
			Error: err.Error(),
			Msg:   "資料不完整",
		})
		return
	}

	// 獲取當前用戶
	user, err := GetUserByID(claims.UserID)
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorMsg{
			Error: err.Error(),
			Msg:   "用戶不存在",
		})
		return
	}

	// 加密新密碼
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorMsg{
			Error: err.Error(),
			Msg:   "密碼加密失敗",
		})
		return
	}

	// 更新密碼
	if err := db.Model(&User{}).Where("id = ?", claims.UserID).Update("pwd", string(hashedPassword)).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorMsg{
			Error: err.Error(),
			Msg:   "密碼更新失敗",
		})
		return
	}

	// 吊銷該用戶所有有效的訪問令牌（強制重新登入）
	db.Model(&AccessToken{}).Where("user_id = ? AND is_revoked = ? AND expires_at > ?", user.ID, false, time.Now()).
		Update("is_revoked", true)
	db.Model(&RefreshToken{}).Where("access_token_id IN (SELECT id FROM access_tokens WHERE user_id = ? AND is_revoked = ? AND expires_at > ?)", user.ID, false, time.Now()).
		Update("is_revoked", true)

	c.JSON(http.StatusOK, gin.H{
		"message": "密碼更新成功，請重新登入",
	})
}

// 檢查待處理的強制登出通知
func checkPendingLogoutNotification(userID, deviceID string, conn *websocket.Conn) {
	// 簡單的實現：檢查該用戶是否有其他活躍的token
	// 如果當前連接的設備ID與最新的活躍設備不匹配，發送登出通知

	db := ConnectDB()
	defer CloseDB(db)

	userIDInt, err := strconv.ParseUint(userID, 10, 64)
	if err != nil {
		log.Printf("解析用戶ID失敗: %v", err)
		return
	}

	// 檢查是否有其他活躍的設備
	var activeDeviceCount int64
	db.Model(&UserDevice{}).Where("user_id = ? AND is_active = ?", userIDInt, true).Count(&activeDeviceCount)

	// 如果有多個活躍設備，說明可能有並發登入，發送登出通知
	if activeDeviceCount > 1 {
		// 發送登出通知
		logoutMsg := gin.H{
			"type":    "forced_logout",
			"message": "您的帳號已在另一個設備登入",
		}

		if err := conn.WriteJSON(logoutMsg); err != nil {
			log.Printf("發送待處理的登出通知失敗: %v", err)
		}
	}
}

// 通知並清理用戶設備的WebSocket連接
func notifyAndCleanupUserDevices(userID uint64, excludeDeviceID, message string) {
	userIDStr := strconv.FormatUint(userID, 10)

	// 檢查該用戶是否有活躍的WebSocket連接
	devices, exists := wsConnections[userIDStr]
	if !exists {
		return
	}

	// 創建一個要清理的設備列表
	devicesToCleanup := make([]string, 0)

	// 向該用戶的所有設備發送登出通知
	for deviceID, conn := range devices {
		if deviceID != excludeDeviceID {
			// 檢查連接是否仍然有效
			if conn == nil {
				devicesToCleanup = append(devicesToCleanup, deviceID)
				continue
			}

			// 發送登出消息
			logoutMsg := gin.H{
				"type":    "forced_logout",
				"message": message,
			}

			// 設置較短的寫入超時，避免阻塞
			conn.SetWriteDeadline(time.Now().Add(1 * time.Second))

			// 使用goroutine發送消息，避免阻塞
			go func(c *websocket.Conn, dID string) {
				defer func() {
					if r := recover(); r != nil {
						log.Printf("❌ 向設備 %s 發送消息時發生panic: %v", dID, r)
					}
				}()

				if err := c.WriteJSON(logoutMsg); err != nil {
					log.Printf("❌ 向設備 %s 發送消息失敗: %v", dID, err)
					// 發送失敗時立即關閉連接
					c.Close()
				}
			}(conn, deviceID)

			// 標記為需要清理
			devicesToCleanup = append(devicesToCleanup, deviceID)
		}
	}

	// 延遲清理連接，給客戶端時間處理消息
	go func() {
		time.Sleep(1 * time.Second) // 減少等待時間到1秒

		// 安全地清理連接
		for _, deviceID := range devicesToCleanup {
			// 檢查用戶映射是否還存在
			if userDevices, userExists := wsConnections[userIDStr]; userExists {
				if conn, deviceExists := userDevices[deviceID]; deviceExists {
					// 安全地關閉連接
					func() {
						defer func() {
							if r := recover(); r != nil {
								log.Printf("❌ 關閉連接時發生panic: %v", r)
							}
						}()
						conn.Close()
					}()

					delete(userDevices, deviceID)
				}
			}
		}

		// 如果用戶沒有其他連接，清理用戶映射
		if userDevices, exists := wsConnections[userIDStr]; exists && len(userDevices) == 0 {
			delete(wsConnections, userIDStr)
		}
	}()
}
