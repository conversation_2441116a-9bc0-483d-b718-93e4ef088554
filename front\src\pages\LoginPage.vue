<template>
  <q-page>
    <q-form @submit="onLogin" class="q-py-lg">
      <q-card
        class="q-mx-auto q-py-lg q-px-md"
        style="max-width: min(100%, 28rem)"
      >
        <q-card-section>
          <div class="text-subtitle1 text-center text-weight-bold">
            買彩券做公益 歡迎加入會員
          </div>
        </q-card-section>

        <q-card-section class="q-gutter-md">
          <div class="text-h6">使用者登入</div>
          <q-input
            type="text"
            v-model="account"
            label="手機號碼(帳號)"
            outlined
            lazy-rules
            :rules="[(val) => !!val || '請輸入手機號碼(帳號)']"
          />
          <q-input
            :type="isPwd ? 'password' : 'text'"
            v-model="password"
            label="密碼"
            outlined
            lazy-rules
            :rules="[(val) => !!val || '請輸入密碼']"
          >
            <template v-slot:append>
              <q-icon
                :name="isPwd ? 'visibility_off' : 'visibility'"
                class="cursor-pointer"
                @click="isPwd = !isPwd"
              />
            </template>
          </q-input>
        </q-card-section>

        <q-card-actions align="between">
          <q-toggle
            v-model="rememberMe"
            label="記住帳號"
            size="lg"
            color="toggle"
            dense
            keep-color
          />

          <!-- 忘記密碼 -->
          <!-- <q-btn
            to="/forget-password"
            label="忘記密碼"
            color="primary"
            dense
            flat
          ></q-btn> -->

          <!-- 註冊 -->
          <q-btn
            to="/register"
            label="前往註冊"
            color="primary"
            dense
            flat
          ></q-btn>
        </q-card-actions>

        <q-card-actions>
          <q-btn
            type="submit"
            rounded
            :loading="isLogin"
            label="登入"
            class="full-width q-mt-sm"
            color="login"
            text-color="white"
            size="lg"
            :ripple="{ center: true }"
          />
        </q-card-actions>
      </q-card>
    </q-form>
  </q-page>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import AUTH_API from '@/api/modules/auth';
import { useAuthStore } from '@/stores/auth';
import { handleError } from '@/utils';

defineOptions({
  name: 'LoginPage',
});

const account = ref('');
const password = ref('');
const isPwd = ref(true);
const rememberMe = ref(false);
const isLogin = ref(false);

const router = useRouter();
const authStore = useAuthStore();

// 如果有記住帳號，自動填入
const rememberedAccount = localStorage.getItem('remembered_account');
if (rememberedAccount) {
  account.value = rememberedAccount;
  rememberMe.value = true;
}

const onLogin = async () => {
  isLogin.value = true;

  try {
    // 獲取設備指紋信息
    const { getLoginDeviceId, getDeviceSummary } = await import('@/services/device-identification');
    const deviceId = await getLoginDeviceId();
    const deviceSummary = await getDeviceSummary();

    const response = await AUTH_API.login({
      uid: account.value,
      pwd: password.value,
      device_id: deviceId,
      device_fingerprint: {
        stable_device_id: deviceSummary.stableDeviceId,
        session_id: deviceSummary.sessionId,
        confidence: deviceSummary.confidence,
        platform: deviceSummary.platform,
        screen: deviceSummary.screen
      }
    });

    // 儲存登入資訊
    authStore.login(response.data);

    // 如果記住帳號，保存到 localStorage
    if (rememberMe.value) {
      localStorage.setItem('remembered_account', account.value);
    } else {
      localStorage.removeItem('remembered_account');
    }

    // 登入後檢查管理員權限（基於最新的用戶資料）
    if (authStore.isAdmin()) {
      router.push('/admin/dashboard/user');
    } else {
      router.push('/lotto-results');
    }
  } catch (error) {
    handleError(error);
  } finally {
    isLogin.value = false;
  }
};
</script>
