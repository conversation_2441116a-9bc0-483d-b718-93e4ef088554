/**
 * 用戶代理字串解析工具
 * 解析 User-Agent 字串，提取瀏覽器、操作系統等信息
 */

export interface ParsedUserAgent {
  browser: string;
  browserVersion: string;
  os: string;
  osVersion: string;
  device: string;
  deviceType: 'desktop' | 'mobile' | 'tablet' | 'unknown';
  raw: string;
}

/**
 * 解析用戶代理字串
 * @param userAgent 原始用戶代理字串
 * @returns 解析後的用戶代理信息
 */
export function parseUserAgent(userAgent: string): ParsedUserAgent {
  if (!userAgent) {
    return {
      browser: '未知',
      browserVersion: '',
      os: '未知',
      osVersion: '',
      device: '未知',
      deviceType: 'unknown',
      raw: userAgent
    };
  }

  const ua = userAgent.toLowerCase();
  
  // 解析瀏覽器
  const browser = parseBrowser(ua);
  
  // 解析操作系統
  const os = parseOS(ua);
  
  // 解析設備類型
  const deviceType = parseDeviceType(ua);
  
  // 解析設備信息
  const device = parseDevice(ua, deviceType);

  return {
    browser: browser.name,
    browserVersion: browser.version,
    os: os.name,
    osVersion: os.version,
    device,
    deviceType,
    raw: userAgent
  };
}

/**
 * 解析瀏覽器信息
 */
function parseBrowser(ua: string): { name: string; version: string } {
  const browsers = [
    { name: 'Edge', pattern: /edg(?:e|ios|a)?\/([\d\.]+)/ },
    { name: 'Chrome', pattern: /(?:chrome|crios)\/([\d\.]+)/ },
    { name: 'Firefox', pattern: /(?:firefox|fxios)\/([\d\.]+)/ },
    { name: 'Safari', pattern: /version\/([\d\.]+).*safari/ },
    { name: 'Opera', pattern: /(?:opera|opr)\/([\d\.]+)/ },
    { name: 'Internet Explorer', pattern: /(?:msie |trident.*rv:)([\d\.]+)/ },
  ];

  for (const browser of browsers) {
    const match = ua.match(browser.pattern);
    if (match) {
      return {
        name: browser.name,
        version: match[1] || ''
      };
    }
  }

  return { name: '未知瀏覽器', version: '' };
}

/**
 * 解析操作系統信息
 */
function parseOS(ua: string): { name: string; version: string } {
  const systems = [
    { 
      name: 'Windows', 
      pattern: /windows nt ([\d\.]+)/,
      versions: {
        '10.0': '10',
        '6.3': '8.1',
        '6.2': '8',
        '6.1': '7',
        '6.0': 'Vista',
        '5.1': 'XP'
      }
    },
    { name: 'macOS', pattern: /mac os x ([\d_\.]+)/ },
    { name: 'iOS', pattern: /os ([\d_\.]+) like mac os x/ },
    { name: 'Android', pattern: /android ([\d\.]+)/ },
    { name: 'Linux', pattern: /linux/ },
    { name: 'Ubuntu', pattern: /ubuntu/ },
  ];

  for (const system of systems) {
    const match = ua.match(system.pattern);
    if (match) {
      let version = match[1] || '';
      
      // Windows 版本映射
      if (system.name === 'Windows' && system.versions) {
        version = (system.versions as any)[version] || version;
      }
      
      // 格式化版本號
      if (version) {
        version = version.replace(/_/g, '.');
      }
      
      return {
        name: system.name,
        version
      };
    }
  }

  return { name: '未知系統', version: '' };
}

/**
 * 解析設備類型
 */
function parseDeviceType(ua: string): 'desktop' | 'mobile' | 'tablet' | 'unknown' {
  if (/mobile|android|iphone|ipod|blackberry|windows phone/i.test(ua)) {
    return 'mobile';
  }
  
  if (/tablet|ipad|android(?!.*mobile)/i.test(ua)) {
    return 'tablet';
  }
  
  if (/windows|macintosh|linux/i.test(ua)) {
    return 'desktop';
  }
  
  return 'unknown';
}

/**
 * 解析設備信息
 */
function parseDevice(ua: string, deviceType: string): string {
  // iPhone
  const iphoneMatch = ua.match(/iphone os ([\d_]+)/);
  if (iphoneMatch) {
    return `iPhone (iOS ${iphoneMatch[1].replace(/_/g, '.')})`;
  }
  
  // iPad
  const ipadMatch = ua.match(/ipad.*os ([\d_]+)/);
  if (ipadMatch) {
    return `iPad (iOS ${ipadMatch[1].replace(/_/g, '.')})`;
  }
  
  // Android 設備
  const androidMatch = ua.match(/android ([\d\.]+).*;\s*([^)]+)\)/);
  if (androidMatch) {
    return `${androidMatch[2]} (Android ${androidMatch[1]})`;
  }
  
  // Windows
  if (ua.includes('windows')) {
    return 'Windows 電腦';
  }
  
  // macOS
  if (ua.includes('macintosh')) {
    return 'Mac 電腦';
  }
  
  // Linux
  if (ua.includes('linux')) {
    return 'Linux 電腦';
  }
  
  return deviceType === 'mobile' ? '手機' : 
         deviceType === 'tablet' ? '平板' : 
         deviceType === 'desktop' ? '電腦' : '未知設備';
}

/**
 * 格式化用戶代理信息為簡短顯示
 * @param parsed 解析後的用戶代理信息
 * @returns 格式化的簡短字串
 */
export function formatUserAgentShort(parsed: ParsedUserAgent): string {
  const parts = [];
  
  if (parsed.browser !== '未知瀏覽器') {
    parts.push(parsed.browser);
  }
  
  if (parsed.os !== '未知系統') {
    parts.push(parsed.os);
  }
  
  return parts.length > 0 ? parts.join(' / ') : '未知';
}

/**
 * 格式化用戶代理信息為詳細顯示
 * @param parsed 解析後的用戶代理信息
 * @returns 格式化的詳細字串
 */
export function formatUserAgentDetailed(parsed: ParsedUserAgent): string {
  const parts = [];
  
  // 瀏覽器信息
  if (parsed.browser !== '未知瀏覽器') {
    const browserInfo = parsed.browserVersion 
      ? `${parsed.browser} ${parsed.browserVersion}`
      : parsed.browser;
    parts.push(`瀏覽器: ${browserInfo}`);
  }
  
  // 操作系統信息
  if (parsed.os !== '未知系統') {
    const osInfo = parsed.osVersion 
      ? `${parsed.os} ${parsed.osVersion}`
      : parsed.os;
    parts.push(`系統: ${osInfo}`);
  }
  
  // 設備信息
  if (parsed.device !== '未知設備') {
    parts.push(`設備: ${parsed.device}`);
  }
  
  return parts.join('\n');
}

/**
 * 獲取設備類型圖標
 * @param deviceType 設備類型
 * @returns 對應的圖標名稱
 */
export function getDeviceTypeIcon(deviceType: string): string {
  switch (deviceType) {
    case 'mobile':
      return 'smartphone';
    case 'tablet':
      return 'tablet';
    case 'desktop':
      return 'computer';
    default:
      return 'device_unknown';
  }
}

/**
 * 獲取瀏覽器圖標
 * @param browser 瀏覽器名稱
 * @returns 對應的圖標名稱或顏色
 */
export function getBrowserIcon(browser: string): { icon: string; color: string } {
  const browserLower = browser.toLowerCase();
  
  if (browserLower.includes('chrome')) {
    return { icon: 'web', color: 'blue' };
  }
  if (browserLower.includes('firefox')) {
    return { icon: 'web', color: 'orange' };
  }
  if (browserLower.includes('safari')) {
    return { icon: 'web', color: 'blue-grey' };
  }
  if (browserLower.includes('edge')) {
    return { icon: 'web', color: 'blue' };
  }
  if (browserLower.includes('opera')) {
    return { icon: 'web', color: 'red' };
  }
  
  return { icon: 'web', color: 'grey' };
}
