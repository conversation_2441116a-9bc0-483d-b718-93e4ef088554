/**
 * 設備識別服務
 * 結合前端設備指紋和後端邏輯，提供更準確的設備識別
 */

import { generateDeviceFingerprint, getSimpleDeviceFingerprint, type DeviceFingerprint } from '@/utils/device-fingerprint';

export interface DeviceInfo {
  deviceId: string;
  deviceFingerprint?: DeviceFingerprint;
  userAgent: string;
  timestamp: number;
  sessionId: string;
}

class DeviceIdentificationService {
  private static instance: DeviceIdentificationService;
  private deviceInfo: DeviceInfo | null = null;
  private fingerprintPromise: Promise<DeviceFingerprint> | null = null;

  private constructor() {}

  public static getInstance(): DeviceIdentificationService {
    if (!DeviceIdentificationService.instance) {
      DeviceIdentificationService.instance = new DeviceIdentificationService();
    }
    return DeviceIdentificationService.instance;
  }

  /**
   * 獲取設備信息
   * @param forceRefresh 是否強制刷新
   * @returns Promise<DeviceInfo>
   */
  public async getDeviceInfo(forceRefresh = false): Promise<DeviceInfo> {
    if (this.deviceInfo && !forceRefresh) {
      return this.deviceInfo;
    }

    // 生成會話ID
    const sessionId = this.generateSessionId();

    // 獲取設備指紋
    const deviceFingerprint = await this.getDeviceFingerprint();

    // 創建設備信息
    this.deviceInfo = {
      deviceId: deviceFingerprint.deviceId,
      deviceFingerprint,
      userAgent: navigator.userAgent,
      timestamp: Date.now(),
      sessionId
    };

    // 保存到 sessionStorage 以便在同一會話中重用
    try {
      sessionStorage.setItem('device-info', JSON.stringify({
        deviceId: this.deviceInfo.deviceId,
        sessionId: this.deviceInfo.sessionId,
        timestamp: this.deviceInfo.timestamp
      }));
    } catch (e) {
      console.warn('無法保存設備信息到 sessionStorage:', e);
    }

    return this.deviceInfo;
  }

  /**
   * 獲取設備指紋
   * @returns Promise<DeviceFingerprint>
   */
  private async getDeviceFingerprint(): Promise<DeviceFingerprint> {
    if (!this.fingerprintPromise) {
      this.fingerprintPromise = this.generateFingerprint();
    }
    return this.fingerprintPromise;
  }

  /**
   * 生成設備指紋
   * @returns Promise<DeviceFingerprint>
   */
  private async generateFingerprint(): Promise<DeviceFingerprint> {
    try {
      // 首先嘗試從緩存獲取
      const cached = this.getCachedFingerprint();
      if (cached && this.isFingerprintValid(cached)) {
        return cached;
      }

      // 生成新的指紋
      const fingerprint = await generateDeviceFingerprint();
      
      // 緩存指紋（24小時有效）
      this.cacheFingerprint(fingerprint);
      
      return fingerprint;
    } catch (error) {
      console.warn('生成設備指紋失敗，使用簡化版本:', error);
      
      // 降級到簡化指紋
      return {
        deviceId: getSimpleDeviceFingerprint(),
        components: {} as any,
        confidence: 50
      };
    }
  }

  /**
   * 從緩存獲取指紋
   */
  private getCachedFingerprint(): DeviceFingerprint | null {
    try {
      const cached = localStorage.getItem('device-fingerprint');
      if (cached) {
        const data = JSON.parse(cached);
        if (data.fingerprint && data.timestamp) {
          return data.fingerprint;
        }
      }
    } catch (e) {
      console.warn('讀取緩存指紋失敗:', e);
    }
    return null;
  }

  /**
   * 緩存指紋
   */
  private cacheFingerprint(fingerprint: DeviceFingerprint): void {
    try {
      const cacheData = {
        fingerprint,
        timestamp: Date.now()
      };
      localStorage.setItem('device-fingerprint', JSON.stringify(cacheData));
    } catch (e) {
      console.warn('緩存指紋失敗:', e);
    }
  }

  /**
   * 檢查指紋是否有效
   */
  private isFingerprintValid(fingerprint: DeviceFingerprint): boolean {
    try {
      const cached = localStorage.getItem('device-fingerprint');
      if (cached) {
        const data = JSON.parse(cached);
        const age = Date.now() - data.timestamp;
        const maxAge = 24 * 60 * 60 * 1000; // 24小時
        return age < maxAge;
      }
    } catch (e) {
      console.warn('檢查指紋有效性失敗:', e);
    }
    return false;
  }

  /**
   * 生成會話ID
   */
  private generateSessionId(): string {
    // 檢查是否已有會話ID
    try {
      const existing = sessionStorage.getItem('session-id');
      if (existing) {
        return existing;
      }
    } catch (e) {
      console.warn('讀取會話ID失敗:', e);
    }

    // 生成新的會話ID
    const sessionId = this.generateUniqueId();
    
    try {
      sessionStorage.setItem('session-id', sessionId);
    } catch (e) {
      console.warn('保存會話ID失敗:', e);
    }
    
    return sessionId;
  }

  /**
   * 生成唯一ID
   */
  private generateUniqueId(): string {
    const timestamp = Date.now().toString(36);
    const randomPart = Math.random().toString(36).substring(2);
    return `${timestamp}-${randomPart}`;
  }

  /**
   * 獲取用於登入的設備標識
   * 這個方法返回一個結合了設備指紋和會話信息的標識
   */
  public async getLoginDeviceId(): Promise<string> {
    const deviceInfo = await this.getDeviceInfo();
    
    // 結合設備指紋和會話ID，但主要依賴設備指紋
    // 這樣同一設備的不同瀏覽器會有不同的會話ID，但設備指紋相同
    return `${deviceInfo.deviceId}-${deviceInfo.sessionId}`;
  }

  /**
   * 獲取設備的穩定標識
   * 這個方法返回純粹基於硬件特徵的標識，不包含會話信息
   */
  public async getStableDeviceId(): Promise<string> {
    const deviceInfo = await this.getDeviceInfo();
    return deviceInfo.deviceId;
  }

  /**
   * 檢查兩個設備ID是否來自同一物理設備
   */
  public async isSamePhysicalDevice(deviceId1: string, deviceId2: string): Promise<boolean> {
    // 提取設備指紋部分（去除會話ID）
    const extractDeviceFingerprint = (id: string) => {
      const parts = id.split('-');
      return parts[0]; // 設備指紋部分
    };

    const fingerprint1 = extractDeviceFingerprint(deviceId1);
    const fingerprint2 = extractDeviceFingerprint(deviceId2);

    return fingerprint1 === fingerprint2;
  }

  /**
   * 清除緩存的設備信息
   */
  public clearCache(): void {
    this.deviceInfo = null;
    this.fingerprintPromise = null;
    
    try {
      localStorage.removeItem('device-fingerprint');
      sessionStorage.removeItem('device-info');
      sessionStorage.removeItem('session-id');
    } catch (e) {
      console.warn('清除設備信息緩存失敗:', e);
    }
  }

  /**
   * 獲取設備信息摘要（用於調試）
   */
  public async getDeviceSummary(): Promise<{
    deviceId: string;
    stableDeviceId: string;
    sessionId: string;
    confidence: number;
    userAgent: string;
    platform: string;
    screen: string;
  }> {
    const deviceInfo = await this.getDeviceInfo();
    const stableDeviceId = await this.getStableDeviceId();
    
    return {
      deviceId: deviceInfo.deviceId,
      stableDeviceId,
      sessionId: deviceInfo.sessionId,
      confidence: deviceInfo.deviceFingerprint?.confidence || 0,
      userAgent: deviceInfo.userAgent,
      platform: navigator.platform,
      screen: `${screen.width}x${screen.height}`
    };
  }
}

// 導出單例實例
export const deviceIdentificationService = DeviceIdentificationService.getInstance();

// 導出便捷方法
export const getDeviceInfo = () => deviceIdentificationService.getDeviceInfo();
export const getLoginDeviceId = () => deviceIdentificationService.getLoginDeviceId();
export const getStableDeviceId = () => deviceIdentificationService.getStableDeviceId();
export const getDeviceSummary = () => deviceIdentificationService.getDeviceSummary();
export const clearDeviceCache = () => deviceIdentificationService.clearCache();
